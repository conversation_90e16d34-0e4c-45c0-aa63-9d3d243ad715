import axios from 'axios'
import { useAuthStore } from '../stores/authStore'

// Create axios instance for Microsoft Graph API
const graphClient = axios.create({
  baseURL: 'https://graph.microsoft.com/v1.0',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

class GraphService {
  constructor() {
    this.authStore = useAuthStore()
    this.setupInterceptors()
  }

  setupInterceptors() {
    // Request interceptor to add Graph API token
    graphClient.interceptors.request.use(
      async (config) => {
        try {
          if (this.authStore.isAuthenticated) {
            const { accessToken } = await this.authStore.getGraphToken()
            config.headers.Authorization = `Bearer ${accessToken}`
          }
        } catch (error) {
          console.error('Failed to get Graph API token:', error)
          throw error
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor for error handling
    graphClient.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          console.error('Unauthorized Graph API request - token may be expired')
        }
        return Promise.reject(error)
      }
    )
  }

  // Get current user profile
  async getUserProfile() {
    try {
      const response = await graphClient.get('/me')
      return response.data
    } catch (error) {
      console.error('Failed to get user profile:', error)
      throw error
    }
  }

  // Get user photo
  async getUserPhoto() {
    try {
      const response = await graphClient.get('/me/photo/$value', {
        responseType: 'blob'
      })
      return URL.createObjectURL(response.data)
    } catch (error) {
      console.error('Failed to get user photo:', error)
      throw error
    }
  }

  // Get user's calendar events
  async getCalendarEvents(top = 10) {
    try {
      const response = await graphClient.get(`/me/events?$top=${top}&$orderby=start/dateTime`)
      return response.data.value
    } catch (error) {
      console.error('Failed to get calendar events:', error)
      throw error
    }
  }

  // Generic method for Graph API calls
  async get(endpoint, config = {}) {
    try {
      const response = await graphClient.get(endpoint, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async post(endpoint, data = {}, config = {}) {
    try {
      const response = await graphClient.post(endpoint, data, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  handleError(error) {
    const errorMessage = error.response?.data?.error?.message || error.message || 'Graph API error occurred'
    const statusCode = error.response?.status || 500

    return {
      message: errorMessage,
      statusCode,
      originalError: error,
    }
  }
}

export const graphService = new GraphService()
