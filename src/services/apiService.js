import axios from 'axios'
import { useAuthStore } from '../stores/authStore'

const authStore = useAuthStore()

// Create axios instance with base configuration
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:7046/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  async (config) => {
    try {
      if (authStore.isAuthenticated) {
        const { accessToken, correlationId } = await authStore.getApiToken()
        config.headers.Authorization = `Bearer ${accessToken}`
        config.headers['X-Correlation-ID'] = correlationId
      }
    } catch (error) {
      console.error('Failed to get access token:', error)
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      console.error('Unauthorized request - token may be expired')
    } else if (error.response?.status >= 500) {
      console.error('Server error:', error.response.data)
    }
    return Promise.reject(error)
  }
)

class ApiService {
  // Generic HTTP methods
  async get(endpoint, config = {}) {
    try {
      const response = await apiClient.get(endpoint, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async post(endpoint, data = {}, config = {}) {
    try {
      const response = await apiClient.post(endpoint, data, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async put(endpoint, data = {}, config = {}) {
    try {
      const response = await apiClient.put(endpoint, data, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  async delete(endpoint, config = {}) {
    try {
      const response = await apiClient.delete(endpoint, config)
      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  // Error handler
  handleError(error) {
    const errorMessage = error.response?.data?.message || error.message || 'An unexpected error occurred'
    const statusCode = error.response?.status || 500

    return {
      message: errorMessage,
      statusCode,
      originalError: error,
    }
  }
}

export const apiService = new ApiService()
export { apiClient }