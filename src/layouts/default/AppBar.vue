<template>
  <v-app-bar class="main-toolbar" dark color="primary">
    <v-btn dark variant="text" prepend-icon="holiday_village" to="/">
      EAMS
    </v-btn>
    <v-spacer></v-spacer>
    <v-tooltip location="bottom" content-class="tooltip-content">
      <template #activator="{ props }">
        <v-btn
          v-bind="props"
          icon
          size="small"
          class="text-white bg-error"
          @click="logout"
        >
          <v-icon class="ml-1" size="23">logout</v-icon>
        </v-btn>
      </template>
      <span>Sign Out</span>
    </v-tooltip>
  </v-app-bar>
</template>

<script setup>
import { useAuthStore } from "../../stores/authStore";

const authStore = useAuthStore()

const logout = () => {
  authStore.logout()
}
</script>

<style lang="scss">
.tooltip-content {
  background: #e3e4f0 !important;
  color: #1a237e !important;
  font-weight: 600;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
}
</style>
