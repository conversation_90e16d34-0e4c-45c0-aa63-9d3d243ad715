<template>
  <div class="login-wrapper">
    <v-card class="login-card" elevation="2" rounded="lg">
      <!-- Logo and title -->
      <v-card-title class="text-center pb-2">
        <div class="d-flex align-center justify-center">
          <v-icon icon="holiday_village" color="primary" size="32" class="mr-2"></v-icon>
          <h2 class="text-primary">EAMS</h2>
        </div>
      </v-card-title>

      <v-card-subtitle class="text-center pb-4">
        Sign in with Microsoft EntraID
      </v-card-subtitle>

      <v-card-text>
        <!-- Error Message -->
        <v-alert
          v-if="authStore.error"
          type="error"
          variant="tonal"
          class="mb-4"
          closable
          @click:close="authStore.clearError"
        >
          {{ authStore.error }}
        </v-alert>

        <!-- Microsoft Login Button -->
        <v-btn
          :loading="authStore.loading"
          @click="loginWithMicrosoft"
          color="primary"
          variant="elevated"
          block
          size="large"
          prepend-icon="login"
        >
          Sign in with Microsoft
        </v-btn>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup>
import { useAuthStore } from '../stores/authStore'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const loginWithMicrosoft = async () => {
  try {
    await authStore.login()
    router.push('/')
  } catch (error) {
    console.error('Login failed:', error)
  }
}
</script>

<style scoped lang="scss">
.login-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 32px;
}
</style>
