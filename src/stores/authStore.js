import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authService } from '../services/authService'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const isAuthenticated = ref(false)
  const accessToken = ref(null)
  const correlationId = ref(null)

  // Getters
  const userInfo = computed(() => {
    return authService.getUserInfo()
  })

  const initialize = async () => {
    loading.value = true
    try {
      const account = await authService.initialize()
      if (account) {
        user.value = account
        isAuthenticated.value = true
        accessToken.value, correlationId.value = await authService.getAccessToken()
      }
    } catch (error) {
      console.error('Auth initialization failed:', error)
    } finally {
      loading.value = false
    }
  }

  const login = async () => {
    loading.value = true
    error.value = null

    try {
      const account = await authService.login()
      user.value = account
      isAuthenticated.value = true
      accessToken.value, correlationId.value = await authService.getAccessToken()
      return account
    } catch (err) {
      error.value = err.message || 'Login failed'
      console.error('Login failed:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    loading.value = true
    error.value = null

    try {
      await authService.logout()
      user.value = null
      isAuthenticated.value = false
      accessToken.value = null
      correlationId.value= null
    } catch (err) {
      error.value = err.message || 'Logout failed'
      console.error('Logout failed:', err)
    } finally {
      loading.value = false
    }
  }

  const getAccessToken = async (scopeType = 'api') => {
    try {
      return await authService.getAccessToken(scopeType)
    } catch (err) {
      error.value = err.message || 'Failed to get access token'
      throw err
    }
  }

  const refreshToken = async () => {
    try {
      accessToken.value, correlationId.value = await authService.getAccessToken()
    } catch(error) {
      console.error('Token refresh failed:', error)
      logout()
      throw error
    }
  }

  const getApiToken = async () => {
    return getAccessToken('api')
  }

  const getGraphToken = async () => {
    return getAccessToken('graph')
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user,
    loading,
    error,
    isAuthenticated,
    accessToken,
    correlationId,

    // Getters
    userInfo,

    // Actions
    initialize,
    login,
    logout,
    getAccessToken,
    refreshToken,
    getApiToken,
    getGraphToken,
    clearError,
  }
})