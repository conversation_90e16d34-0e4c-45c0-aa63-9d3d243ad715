/**
 * plugins/vuetify.js
 *
 * Framework documentation: https://vuetifyjs.com
 */

import { createVuetify } from 'vuetify'
import 'vuetify/styles'
import "material-design-icons-iconfont/dist/material-design-icons.css"

// Import Vuetify components and directives
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
import { aliases, md } from "vuetify/iconsets/md"

export default createVuetify({
  components,
  directives,
  theme: {
    defaultTheme: 'light',
    themes: {
      light: {
        colors: {
          primary: '#1A237E',
          secondary: '#e3e4f0'
        },
      },
    },
  },
  icons: {
    defaultSet: 'md',
    aliases,
    sets: {
      md,
    }
  },
})
