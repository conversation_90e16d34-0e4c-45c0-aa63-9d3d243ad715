import { createRouter, createWebHistory } from 'vue-router' 
import { useAuthStore } from '../stores/authStore'

const routes = [
  {
    path: '/',
    component: () => import("../layouts/default/DefaultLayout.vue"),
    children: [
      {
        path: "",
        name: 'AccommodationIndex',
        component: () => import("../views/AccommodationIndex.vue"),
        meta: { requiresAuth: true },
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import("../views/LoginView.vue"),
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})


// Navigation guard for authentication
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Wait for authentication to be properly initialized
  const isAuthenticated = await authStore.isAuthenticated
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  console.log('Router guard:', {
    to: to.path,
    from: from.path,
    requiresAuth,
    isAuthenticated
  })

  if (requiresAuth && !isAuthenticated) {
    // Redirect to login if route requires auth and user is not authenticated
    next('/login')
  } else if (to.name === 'Login' && isAuthenticated) {
    // Redirect to home if user is already authenticated and tries to access login
    next('/')
  } else {
    next()
  }
})

export default router