# Entra ID Configuration
VUE_APP_CLIENT_ID=your-client-id-here
VUE_APP_CLIENT_SECRET=your-client-secret-here
VUE_APP_API_TENANT_ID=your-tenant-id-here
VUE_APP_API_CLIENT_ID=your-api-client-id-here
# API Scopes - comma-separated for multiple scopes
VUE_APP_API_SCOPES=api://your-api-client-id/scope1,api://your-api-client-id/scope2
# Graph API Scopes - comma-separated for multiple scopes
VUE_APP_GRAPH_SCOPES=https://graph.microsoft.com/User.Read,https://graph.microsoft.com/profile
VUE_APP_API_AUTHORITY=https://login.microsoftonline.com/${VUE_APP_API_TENANT_ID}

# Backend API Configuration
VUE_APP_API_BASE_URL=https://localhost:7046/api

# Server Configuration
PORT=5137